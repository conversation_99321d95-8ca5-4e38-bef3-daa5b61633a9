#!/usr/bin/env python3
"""
Test inline input in output area
"""

def main():
    print("Welcome to Interactive Python!")
    print("You can type directly in the output area below.")
    print("-" * 40)
    
    # First input - should show input field in output
    name = input("What's your name? ")
    print(f"Hello, {name}! Nice to meet you.")
    
    # Second input
    age = input("How old are you? ")
    
    try:
        age = int(age)
        print(f"Great! You are {age} years old.")
        print(f"Next year you will be {age + 1}!")
    except ValueError:
        print("That's not a valid number, but that's okay!")
    
    # Third input
    color = input("What's your favorite color? ")
    print(f"Wow, {color} is a beautiful color!")
    
    print("\nAll done! 🎉")

if __name__ == "__main__":
    main()
