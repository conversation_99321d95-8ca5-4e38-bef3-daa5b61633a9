#!/usr/bin/env python3
"""
Quick test to verify the UnboundLocalError fix
"""

# Test the exact code that was failing
numbers = [1, 2, 3, 4, 5]
print("Numbers:", numbers)
squares = [x**2 for x in numbers]
squares  # This should show: [1, 4, 9, 16, 25]

# Test input function
name = input("What's your name? ")
print(f"Hello, {name}!")

# Test more complex expressions
data = {"a": 1, "b": 2, "c": 3}
print("Data:", data)
data

# Test calculations
result = sum(numbers)
print(f"Sum: {result}")
result * 2
