#!/usr/bin/env python3
"""
Test real interactive input functionality
"""

def main():
    print("Welcome to the Interactive Python Demo!")
    print("=" * 40)
    
    # First input
    name = input("What's your name? ")
    print(f"Hello, {name}! Nice to meet you.")
    
    # Second input
    age = input("How old are you? ")
    
    try:
        age = int(age)
        print(f"Great! You are {age} years old.")
        print(f"Next year you will be {age + 1}!")
        
        if age >= 18:
            print("You are an adult! 🎉")
        else:
            print("You are still young! 🌟")
            
    except ValueError:
        print("That's not a valid number, but that's okay!")
    
    # Third input
    favorite_color = input("What's your favorite color? ")
    print(f"Wow, {favorite_color} is a beautiful color!")
    
    # Final message
    print(f"\nSummary:")
    print(f"Name: {name}")
    print(f"Age: {age}")
    print(f"Favorite Color: {favorite_color}")
    print("\nThanks for using the interactive demo! 🎈")

if __name__ == "__main__":
    main()
