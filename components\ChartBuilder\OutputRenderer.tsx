'use client'

import { useEffect, useRef, useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface OutputRendererProps {
  output?: string
  plots?: string[]
  result?: any
  className?: string
}

export function OutputRenderer({ output, plots, result, className }: OutputRendererProps) {
  const plotlyRef = useRef<HTMLDivElement>(null)
  const [plotlyLoaded, setPlotlyLoaded] = useState(false)

  // Load Plotly.js dynamically with better CORS handling
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.Plotly) {
      const script = document.createElement('script')
      // Use unpkg.com which has better CORS support
      script.src = 'https://unpkg.com/plotly.js-dist@latest/plotly.js'
      script.crossOrigin = 'anonymous'
      script.onload = () => {
        console.log('Plotly.js loaded successfully')
        setPlotlyLoaded(true)
      }
      script.onerror = (error) => {
        console.error('Failed to load Plotly.js:', error)
        // Try alternative CDN
        const fallbackScript = document.createElement('script')
        fallbackScript.src = 'https://cdn.jsdelivr.net/npm/plotly.js-dist@latest/plotly.min.js'
        fallbackScript.crossOrigin = 'anonymous'
        fallbackScript.onload = () => {
          console.log('Plotly.js loaded from fallback CDN')
          setPlotlyLoaded(true)
        }
        fallbackScript.onerror = () => {
          console.error('All Plotly.js CDNs failed to load')
        }
        document.head.appendChild(fallbackScript)
      }
      document.head.appendChild(script)
    } else if (window.Plotly) {
      setPlotlyLoaded(true)
    }
  }, [])

  // Render Plotly plots
  useEffect(() => {
    if (!plotlyLoaded || !plotlyRef.current) return

    // Clear previous plots
    plotlyRef.current.innerHTML = ''

    console.log('OutputRenderer: Checking for Plotly data', { result, plots })

    // Handle result with Plotly data
    if (result && typeof result === 'object') {
      // Check for Plotly JSON format
      if (result['application/vnd.plotly.v1+json']) {
        console.log('Found Plotly data in result.application/vnd.plotly.v1+json')
        const plotlyData = result['application/vnd.plotly.v1+json']
        renderPlotlyPlot(plotlyData)
        return
      }

      // Check for text/html with Plotly
      if (result['text/html'] && result['text/html'].includes('plotly')) {
        console.log('Found Plotly HTML in result.text/html')
        renderPlotlyHTML(result['text/html'])
        return
      }
    }

    // Handle plots array
    if (plots && plots.length > 0) {
      console.log('OutputRenderer: Found plots array', plots)
      plots.forEach((plot, index) => {
        console.log(`OutputRenderer: Processing plot ${index}`, { plot: typeof plot === 'string' ? plot.substring(0, 200) + '...' : plot })

        // Check for Plotly plots (JSON format)
        if (typeof plot === 'string' && (
          plot.includes('plotly') ||
          plot.includes('application/vnd.plotly.v1+json') ||
          (plot.startsWith('{') && (plot.includes('"data"') || plot.includes('"layout"')))
        )) {
          try {
            const plotData = JSON.parse(plot)
            console.log('OutputRenderer: Parsed plot data', plotData)

            // Check if it's a direct Plotly figure
            if (plotData.data && plotData.layout) {
              console.log('OutputRenderer: Rendering direct Plotly figure')
              renderPlotlyPlot(plotData, `plot-${index}`)
            }
            // Check if it's wrapped in application/vnd.plotly.v1+json
            else if (plotData['application/vnd.plotly.v1+json']) {
              console.log('OutputRenderer: Rendering wrapped Plotly figure')
              renderPlotlyPlot(plotData['application/vnd.plotly.v1+json'], `plot-${index}`)
            }
            // Check for text/html with Plotly content
            else if (plotData['text/html'] && plotData['text/html'].includes('plotly')) {
              console.log('OutputRenderer: Rendering Plotly HTML')
              renderPlotlyHTML(plotData['text/html'])
            }
          } catch (error) {
            console.error('Error parsing Plotly data:', error)
            console.log('Raw plot data:', plot.substring(0, 500))
          }
        }
      })
    }
  }, [plotlyLoaded, result, plots])

  const renderPlotlyPlot = (plotlyData: any, containerId?: string) => {
    if (!window.Plotly || !plotlyRef.current) {
      console.error('Plotly not loaded or container not available')
      return
    }

    console.log('Rendering Plotly plot with data:', plotlyData)

    try {
      // Process the data to handle binary data
      const processedData = plotlyData.data?.map((trace: any) => {
        const processedTrace = { ...trace }

        // Handle binary data in x and y
        if (trace.x && typeof trace.x === 'object' && trace.x.bdata) {
          console.log('Decoding x data:', trace.x)
          processedTrace.x = decodeBinaryData(trace.x.bdata, trace.x.dtype)
          console.log('Decoded x data:', processedTrace.x)
        }
        if (trace.y && typeof trace.y === 'object' && trace.y.bdata) {
          console.log('Decoding y data:', trace.y)
          processedTrace.y = decodeBinaryData(trace.y.bdata, trace.y.dtype)
          console.log('Decoded y data:', processedTrace.y)
        }

        return processedTrace
      }) || []

      console.log('Processed plot data:', processedData)

      const layout = {
        ...plotlyData.layout,
        autosize: true,
        responsive: true,
        margin: { l: 50, r: 50, t: 50, b: 50 }
      }

      const config = {
        responsive: true,
        displayModeBar: true,
        displaylogo: false,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d']
      }

      // Create a container for this specific plot
      const plotContainer = document.createElement('div')
      plotContainer.style.width = '100%'
      plotContainer.style.height = '400px'
      plotContainer.style.marginBottom = '16px'
      plotContainer.style.border = '1px solid #e2e8f0'
      plotContainer.style.borderRadius = '8px'

      if (containerId) {
        plotContainer.id = containerId
      }

      plotlyRef.current.appendChild(plotContainer)

      console.log('Creating Plotly plot...')
      window.Plotly.newPlot(plotContainer, processedData, layout, config)
        .then(() => {
          console.log('Plotly plot created successfully')
        })
        .catch((error: any) => {
          console.error('Plotly.newPlot failed:', error)
          throw error
        })
    } catch (error) {
      console.error('Error rendering Plotly plot:', error)
      if (plotlyRef.current) {
        plotlyRef.current.innerHTML = `
          <div class="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-center">
            <div class="text-red-600 dark:text-red-400">
              <p>❌ Failed to render Plotly plot</p>
              <p class="text-xs mt-1">Error: ${error.message}</p>
              <details class="mt-2 text-left">
                <summary class="cursor-pointer">Debug Info</summary>
                <pre class="text-xs mt-1 overflow-auto">${JSON.stringify(plotlyData, null, 2)}</pre>
              </details>
            </div>
          </div>
        `
      }
    }
  }

  const renderPlotlyHTML = (html: string) => {
    if (!plotlyRef.current) return

    const iframe = document.createElement('iframe')
    iframe.style.width = '100%'
    iframe.style.height = '400px'
    iframe.style.border = 'none'
    iframe.style.borderRadius = '8px'
    iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin')
    iframe.srcdoc = html

    plotlyRef.current.appendChild(iframe)
  }

  // Decode binary data from Plotly
  const decodeBinaryData = (bdata: string, dtype: string): number[] => {
    try {
      const buffer = Uint8Array.from(atob(bdata), c => c.charCodeAt(0))
      
      switch (dtype) {
        case 'i1': // int8
          return Array.from(new Int8Array(buffer.buffer))
        case 'i2': // int16
          return Array.from(new Int16Array(buffer.buffer))
        case 'i4': // int32
          return Array.from(new Int32Array(buffer.buffer))
        case 'f4': // float32
          return Array.from(new Float32Array(buffer.buffer))
        case 'f8': // float64
          return Array.from(new Float64Array(buffer.buffer))
        default:
          return Array.from(buffer)
      }
    } catch (error) {
      console.error('Error decoding binary data:', error)
      return []
    }
  }

  const renderTextOutput = (text: string) => {
    // Handle ANSI escape codes for colored output
    const ansiToHtml = (str: string) => {
      return str
        .replace(/\x1b\[31m/g, '<span style="color: #ef4444;">')  // red
        .replace(/\x1b\[32m/g, '<span style="color: #22c55e;">')  // green
        .replace(/\x1b\[33m/g, '<span style="color: #eab308;">')  // yellow
        .replace(/\x1b\[34m/g, '<span style="color: #3b82f6;">')  // blue
        .replace(/\x1b\[35m/g, '<span style="color: #a855f7;">')  // magenta
        .replace(/\x1b\[36m/g, '<span style="color: #06b6d4;">')  // cyan
        .replace(/\x1b\[37m/g, '<span style="color: #6b7280;">')  // white
        .replace(/\x1b\[0m/g, '</span>')  // reset
        .replace(/\x1b\[\d+m/g, '')  // remove other codes
    }

    return (
      <div className="font-mono text-sm whitespace-pre-wrap bg-gray-50 dark:bg-gray-900 p-3 rounded border overflow-auto max-h-96">
        <div dangerouslySetInnerHTML={{ __html: ansiToHtml(text) }} />
      </div>
    )
  }

  const renderJSONOutput = (data: any) => {
    return (
      <div className="font-mono text-sm bg-gray-50 dark:bg-gray-900 p-3 rounded border overflow-auto max-h-96">
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </div>
    )
  }

  const renderHTMLOutput = (html: string) => {
    return (
      <div className="border rounded overflow-hidden">
        <iframe
          srcDoc={html}
          className="w-full border-none"
          style={{ height: '400px', minHeight: '300px' }}
          sandbox="allow-scripts allow-same-origin allow-forms"
          title="HTML Output"
        />
      </div>
    )
  }

  // Determine what to render
  const hasPlotlyData = result && (
    result['application/vnd.plotly.v1+json'] || 
    (result['text/html'] && result['text/html'].includes('plotly'))
  )

  const hasHTMLOutput = output && (
    output.includes('<html') || 
    output.includes('<div') || 
    output.includes('<script')
  )

  if (!output && !plots?.length && !result && !hasPlotlyData) {
    return null
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Plotly Plots */}
      {hasPlotlyData && (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-sm font-medium">📊 Interactive Plot</span>
          </div>
          <div ref={plotlyRef} className="w-full" />
          {!plotlyLoaded && (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              <div className="text-center">
                <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2" />
                <p className="text-sm">Loading Plotly...</p>
              </div>
            </div>
          )}
        </Card>
      )}

      {/* Regular Plots */}
      {plots && plots.length > 0 && !hasPlotlyData && (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-sm font-medium">🎨 Plots ({plots.length})</span>
          </div>
          <div className="space-y-4">
            {plots.map((plot, index) => {
              if (plot.length < 100 && !plot.startsWith('http')) {
                return (
                  <div key={index} className="p-3 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded text-center">
                    <p className="text-yellow-600 dark:text-yellow-400 text-sm">
                      ⚠️ Plot {index + 1}: No image data generated
                    </p>
                  </div>
                )
              }

              let imgSrc = plot
              if (!plot.startsWith('data:') && !plot.startsWith('http')) {
                imgSrc = `data:image/png;base64,${plot}`
              }

              return (
                <div key={index} className="text-center">
                  <img
                    src={imgSrc}
                    alt={`Plot ${index + 1}`}
                    className="max-w-full h-auto rounded border shadow-sm mx-auto"
                    style={{ maxHeight: '500px' }}
                  />
                </div>
              )
            })}
          </div>
        </Card>
      )}

      {/* Text/HTML Output */}
      {output && (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-sm font-medium">📄 Output</span>
          </div>
          {hasHTMLOutput ? renderHTMLOutput(output) : renderTextOutput(output)}
        </Card>
      )}

      {/* JSON Result */}
      {result && !hasPlotlyData && (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-sm font-medium">📋 Result</span>
          </div>
          {renderJSONOutput(result)}
        </Card>
      )}
    </div>
  )
}

// Extend window type for Plotly
declare global {
  interface Window {
    Plotly: any
  }
}
