'use client'

import React, { useEffect, useRef, useState } from 'react'
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface OutputRendererProps {
  output?: string
  plots?: string[]
  result?: any
  className?: string
  needsInput?: boolean
  inputPrompt?: string
  onInputSubmit?: (input: string) => void
}

export function OutputRenderer({ 
  output, 
  plots, 
  result, 
  className, 
  needsInput, 
  inputPrompt, 
  onInputSubmit 
}: OutputRendererProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const [userInput, setUserInput] = useState('')

  // Focus input when it becomes available
  useEffect(() => {
    if (needsInput && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [needsInput])

  // Clear input when needsInput changes to false
  useEffect(() => {
    if (!needsInput) {
      setUserInput('')
    }
  }, [needsInput])

  const handleSubmit = () => {
    if (userInput.trim() && onInputSubmit) {
      onInputSubmit(userInput.trim())
      setUserInput('')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSubmit()
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Text Output */}
      {output && (
        <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
            Output
          </div>
          <div className="p-4 font-mono text-sm max-h-96 overflow-y-auto">
            <pre className="whitespace-pre-wrap text-gray-900 dark:text-gray-100 leading-relaxed">
              {output}
            </pre>
            
            {/* Interactive Input Field */}
            {needsInput && (
              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                <div className="text-sm text-blue-700 dark:text-blue-300 mb-3 font-medium">
                  {inputPrompt || 'Enter input:'}
                </div>
                <div className="flex gap-3">
                  <input
                    ref={inputRef}
                    type="text"
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="flex-1 px-3 py-2 text-sm border border-blue-300 dark:border-blue-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Type your answer here..."
                  />
                  <Button
                    onClick={handleSubmit}
                    size="sm"
                    disabled={!userInput.trim()}
                    className="px-6"
                  >
                    Submit
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Plots */}
      {plots && plots.length > 0 && (
        <div className="space-y-4">
          {plots.map((plot, index) => {
            let imgSrc = plot
            if (!plot.startsWith('data:') && !plot.startsWith('http')) {
              imgSrc = `data:image/png;base64,${plot}`
            }

            return (
              <div key={index} className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                  Plot {index + 1}
                </div>
                <div className="p-4 text-center">
                  <img
                    src={imgSrc}
                    alt={`Plot ${index + 1}`}
                    className="max-w-full h-auto rounded border shadow-sm mx-auto"
                    style={{ maxHeight: '500px' }}
                  />
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* Result */}
      {result && (
        <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
            Result
          </div>
          <div className="p-4 font-mono text-sm">
            <pre className="whitespace-pre-wrap text-gray-900 dark:text-gray-100 leading-relaxed">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  )
}
