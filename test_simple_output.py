#!/usr/bin/env python3
"""
Test script to verify simplified Python output functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from jupyter_kernel import get_kernel

def test_simple_output():
    """Test simple print statements and expressions"""
    print("Testing simple output functionality...")
    
    kernel = get_kernel()
    
    # Test 1: Simple print statements
    code1 = """
print("Hello, World!")
print("This is a test")
print(f"2 + 2 = {2 + 2}")
"""
    
    result1 = kernel.execute_code(code1)
    print(f"Test 1 - Print statements:")
    print(f"Status: {result1['status']}")
    print(f"Output: {repr(result1['stdout'])}")
    print()
    
    # Test 2: Expressions and calculations
    code2 = """
x = 10
y = 20
print(f"x = {x}, y = {y}")
x + y
"""
    
    result2 = kernel.execute_code(code2)
    print(f"Test 2 - Expressions:")
    print(f"Status: {result2['status']}")
    print(f"Output: {repr(result2['stdout'])}")
    print()
    
    # Test 3: Lists and data structures
    code3 = """
numbers = [1, 2, 3, 4, 5]
print("Numbers:", numbers)
squares = [x**2 for x in numbers]
squares
"""
    
    result3 = kernel.execute_code(code3)
    print(f"Test 3 - Data structures:")
    print(f"Status: {result3['status']}")
    print(f"Output: {repr(result3['stdout'])}")
    print()
    
    # Test 4: Loops and control flow
    code4 = """
for i in range(3):
    print(f"Iteration {i}")

result = "Loop completed"
result
"""
    
    result4 = kernel.execute_code(code4)
    print(f"Test 4 - Loops:")
    print(f"Status: {result4['status']}")
    print(f"Output: {repr(result4['stdout'])}")
    print()
    
    # Test 5: Functions
    code5 = """
def greet(name):
    return f"Hello, {name}!"

print(greet("Alice"))
print(greet("Bob"))

# Return a value
greet("Charlie")
"""
    
    result5 = kernel.execute_code(code5)
    print(f"Test 5 - Functions:")
    print(f"Status: {result5['status']}")
    print(f"Output: {repr(result5['stdout'])}")
    print()
    
    # Test 6: Input function (should work without hanging)
    code6 = """
name = input("What's your name? ")
print(f"Hello, {name}!")
"""
    
    result6 = kernel.execute_code(code6)
    print(f"Test 6 - Input function:")
    print(f"Status: {result6['status']}")
    print(f"Output: {repr(result6['stdout'])}")
    print()

def test_plotly_simple():
    """Test simple Plotly functionality"""
    print("Testing Plotly output...")
    
    kernel = get_kernel()
    
    code = """
import plotly.graph_objects as go

fig = go.Figure()
fig.add_trace(go.Scatter(x=[1, 2, 3, 4], y=[10, 11, 12, 13], mode='lines+markers'))
fig.update_layout(title='Simple Line Plot')

print("Plot created!")
fig.show()
"""
    
    result = kernel.execute_code(code)
    print(f"Plotly test:")
    print(f"Status: {result['status']}")
    print(f"Output: {repr(result['stdout'])}")
    print(f"Plots: {len(result['plots'])}")
    if result['plots']:
        print(f"Plot data length: {len(result['plots'][0])}")

if __name__ == "__main__":
    print("HRatlas Simplified Output Test")
    print("=" * 50)
    
    test_simple_output()
    
    print("\n" + "=" * 50)
    test_plotly_simple()
    
    print("\n" + "=" * 50)
    print("✅ All tests completed!")
    print("The output should now work like a normal Python terminal/Jupyter notebook.")
    print("- Print statements appear immediately")
    print("- Expression results are shown")
    print("- No complex input dialogs needed")
    print("- Plotly plots are captured and displayed")
