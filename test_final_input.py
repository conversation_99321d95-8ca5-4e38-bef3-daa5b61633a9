#!/usr/bin/env python3
"""
Final test for interactive input functionality
"""

print("🎯 Final Interactive Input Test")
print("=" * 40)

# Test 1: Basic input
name = input("What's your name? ")
print(f"Hello, {name}!")

# Test 2: Numeric input with validation
while True:
    age_str = input("How old are you? ")
    try:
        age = int(age_str)
        break
    except ValueError:
        print("Please enter a valid number!")

print(f"You are {age} years old!")

# Test 3: Choice input
color = input("What's your favorite color? ")
print(f"Nice choice! {color} is beautiful.")

# Test 4: Final summary
print("\n" + "=" * 40)
print("Summary:")
print(f"Name: {name}")
print(f"Age: {age}")
print(f"Color: {color}")
print("Test completed successfully! 🎉")

# Test 5: Return a result
result = {"name": name, "age": age, "color": color}
result
