#!/usr/bin/env python3
"""
Test interactive terminal functionality
"""

# Test 1: Simple interactive input
print("Welcome to the Interactive Python Terminal!")
print("=" * 50)

name = input("What's your name? ")
print(f"Hello, {name}! Nice to meet you.")

age = input("How old are you? ")
print(f"Wow, {age} years old! That's awesome.")

# Test 2: Simple calculations with output
print("\nLet's do some calculations:")
numbers = [1, 2, 3, 4, 5]
print("Numbers:", numbers)

squares = [x**2 for x in numbers]
print("Squares:", squares)
squares  # This should also display

# Test 3: Data structures
print("\nWorking with data:")
data = {"name": name, "age": age, "numbers": numbers}
print("Data dictionary:", data)
data  # This should display the dict

# Test 4: Simple loop
print("\nCounting to 5:")
for i in range(1, 6):
    print(f"Count: {i}")

print("\nAll done! 🎉")
