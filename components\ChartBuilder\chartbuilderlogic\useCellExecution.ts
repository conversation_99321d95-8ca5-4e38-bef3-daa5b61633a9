import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Dataset } from '@/types/index';
import { UseCellExecutionReturn, CellData } from './types';
import { checkInternetConnection, initializeAlasqlUtils, isBrowser } from './chartBuilderUtils';

/**
 * Hook for handling cell execution in the ChartBuilder
 * @param setCells Function to update cells state
 * @param cells Current cells state
 * @param datasetCache Dataset cache for quick access
 * @returns Cell execution functions and state
 */
export const useCellExecution = (
  setCells: React.Dispatch<React.SetStateAction<CellData[]>>,
  cells: CellData[],
  datasetCache: Record<string, Dataset>
): UseCellExecutionReturn => {
  const [isAlasqlInitialized, setIsAlasqlInitialized] = useState(false);
  const [currentData, setCurrentData] = useState<any[]>([]);

  // Initialize alasql-utils on component mount
  useEffect(() => {
    initializeAlasqlUtils().then(initialized => {
      setIsAlasqlInitialized(initialized);
    });
  }, []);

  // Execute cell code
  const handleRunCell = async (cellId: string, code: string, showGraphicWalker: boolean = false) => {
    try {
      const cell = cells.find(c => c.id === cellId);
      if (!cell) return;

      // Get the datasets for this specific cell
      const cellDatasetIds = cell.selectedDatasetIds || [];
      
      // For Python, allow execution without dataset selection (users can load their own data)
      if (cellDatasetIds.length === 0 && cell.language !== 'python') {
        toast.error('Please select at least one dataset for this cell first');
        return;
      }

      // Get the actual dataset objects for this cell from the cache
      const cellDatasets = cellDatasetIds
        .map(id => datasetCache[id])
        .filter(ds => ds !== undefined);

      // Add loading state to specific cell
      setCells(prev => prev.map(c =>
        c.id === cellId ? { ...c, isLoading: true } : c
      ));

      let endpoint: string;
      let payload: any;
      let response: Response | undefined;

      // Check for GraphicWalker or loopchart commands
      const hasVisualizationCommand = cell.language === 'sql' &&
        (code.includes("--#graphicwalker") ||
         code.includes("-- #graphicwalker") ||
         code.includes("--loopchart") ||
         code.includes("-- loopchart"));

      // Set the showGraphicWalker flag based on command or explicit parameter
      const shouldShowGraphicWalker = showGraphicWalker || hasVisualizationCommand;

      const startTime = Date.now();

      // All execution now requires server connection

      switch (cell.language) {
        case 'javascript':
          endpoint = '/api/execute-js';
          payload = {
            code,
            datasets: cellDatasets.map(ds => ({
              id: ds.id,
              name: ds.name,
              data: ds.data
            })),
          };
          response = await fetch(endpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
          });
          break;
        case 'python':
          // Use the execute endpoint (now powered by Jupyter kernel)
          endpoint = '/api/execute';

          payload = {
            code,
            datasets: cellDatasets.map(ds => ({ // Send actual dataset data
              id: ds.id,
              name: ds.name,
              data: ds.data
            })),
            language: 'python'
          };
          console.log('Sending Python payload:', payload);

          try {
            response = await fetch(endpoint, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(payload)
            });
          } catch (error) {
            console.error('Python API call failed:', error);
            toast.error('Failed to execute Python code. Please try again.');
            throw new Error('Failed to execute Python code');
          }
          break;
        case 'sql':
          // Use server-side SQL execution via /api/query endpoint
          endpoint = '/api/query';
          payload = {
            query: code.trim(),
            datasets: cellDatasets.map(ds => ({
              id: ds.id,
              name: ds.name,
              data: ds.data
            })),
          };
          console.log('Sending SQL payload with multiple datasets:', payload);

          try {
            response = await fetch(endpoint, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(payload)
            });
          } catch (error) {
            console.error('SQL API call failed:', error);
            toast.error('Failed to execute SQL query. Please try again.');
            throw new Error('Failed to execute SQL query');
          }
          break;
        default:
          throw new Error(`Unsupported language: ${cell.language}`);
      }

      // Check if response is defined before using it
      if (!response) {
        throw new Error(`No response received for ${cell.language} execution`);
      }

      // Now we can safely use response since we've checked it exists
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to execute ${cell.language} code`);
      }

      const executionTime = Date.now() - startTime;

      // Only read the response body ONCE
      const responseData = await response.json();

      // Check for error in responseData
      if (responseData.error) {
        setCells(prev => prev.map(c =>
          c.id === cellId
            ? {
                ...c,
                error: responseData.error,
                errorDetails: responseData.errorDetails,
                executionTime: responseData.executionTime || executionTime,
                isSuccess: false,
                isLoading: false
              }
            : c
        ));
        return;
      }

      // Standardize the result format
      const processedData = Array.isArray(responseData) ? responseData :
                          responseData.data ? responseData.data :
                          [responseData];

      // Success case - Fix structure to match CellProps result type
      setCells(prev => prev.map(c =>
        c.id === cellId
          ? {
              ...c,
              result: {
                data: processedData,
                output: responseData.output,
                plots: responseData.plots || [], // Ensure plots is always an array
                variables: responseData.variables, // Store variables from backend
                variableTypes: responseData.variableTypes, // Store variable types
                outputType: responseData.outputType, // Store output type information
                needs_input: responseData.needs_input, // Store input requirement
                input_prompt: responseData.input_prompt, // Store input prompt
              },
              error: undefined,
              errorDetails: undefined,
              executionTime: responseData.executionTime || executionTime,
              isSuccess: true,
              isLoading: false,
              showGraphicWalker: shouldShowGraphicWalker || responseData.showGraphicWalker
            }
          : c
      ));

      setCurrentData(processedData);
      toast.success(`${cell.language} executed successfully`);

    } catch (error) {
      console.error('Execution error:', error);
      toast.error(error instanceof Error ? error.message : 'Execution failed');

      // Update cell with error
      setCells(prev => prev.map(c =>
        c.id === cellId
          ? {
              ...c,
              error: error instanceof Error ? error.message : 'Execution failed',
              isSuccess: false,
              isLoading: false
            }
          : c
      ));
    }
  };

  return {
    handleRunCell,
    isAlasqlInitialized
  };
};
