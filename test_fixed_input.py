#!/usr/bin/env python3
"""
Test the fixed interactive input functionality
"""

print("🎉 Interactive Python Input Test")
print("=" * 40)

# Test 1: Simple input
name = input("What's your name? ")
print(f"Hello, {name}! Welcome to interactive Python.")

# Test 2: Numeric input
age = input("How old are you? ")
try:
    age_num = int(age)
    print(f"Great! You are {age_num} years old.")
    if age_num >= 18:
        print("You're an adult! 🎂")
    else:
        print("You're still young! 🌟")
except ValueError:
    print("That's not a number, but that's okay!")

# Test 3: Choice input
color = input("What's your favorite color? ")
print(f"Wow, {color} is a beautiful color! 🎨")

# Test 4: Final summary
print("\n" + "=" * 40)
print("Summary:")
print(f"Name: {name}")
print(f"Age: {age}")
print(f"Favorite Color: {color}")
print("Thanks for testing! 🚀")

# Test 5: Expression result
result = len(name) + len(color)
print(f"Your name + color has {result} characters total!")
result  # This should display the result
