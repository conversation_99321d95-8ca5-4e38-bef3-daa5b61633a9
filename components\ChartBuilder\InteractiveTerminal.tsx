'use client'

import React, { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface InteractiveTerminalProps {
  output: string
  onInput?: (input: string) => void
}

export function InteractiveTerminal({ output, onInput }: InteractiveTerminalProps) {
  const [currentInput, setCurrentInput] = useState('')
  const [terminalHistory, setTerminalHistory] = useState<string[]>([])
  const [isWaitingForInput, setIsWaitingForInput] = useState(false)
  const [currentPrompt, setCurrentPrompt] = useState('')
  const inputRef = useRef<HTMLInputElement>(null)
  const terminalRef = useRef<HTMLDivElement>(null)

  // Parse the output to detect input prompts
  useEffect(() => {
    const lines = output.split('\n')
    const history: string[] = []
    let waitingForInput = false
    let prompt = ''

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      
      // Check if this line contains an input prompt
      if (line.includes('[auto-input]')) {
        // Extract the prompt part before [auto-input]
        const promptMatch = line.match(/^(.+?)\[auto-input\]/)
        if (promptMatch) {
          prompt = promptMatch[1].trim()
          history.push(`${prompt}`)
          waitingForInput = true
        }
      } else {
        history.push(line)
      }
    }

    setTerminalHistory(history)
    setIsWaitingForInput(waitingForInput)
    setCurrentPrompt(prompt)
  }, [output])

  // Auto-scroll to bottom
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [terminalHistory, currentInput])

  // Focus input when waiting for input
  useEffect(() => {
    if (isWaitingForInput && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isWaitingForInput])

  const handleInputSubmit = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && currentInput.trim()) {
      // Add the input to history
      setTerminalHistory(prev => [...prev, `${currentInput}`])

      // Send input to backend
      try {
        const response = await fetch('/api/python/input', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ input: currentInput }),
        });

        if (response.ok) {
          // Call the onInput callback if provided
          if (onInput) {
            onInput(currentInput)
          }
        } else {
          console.error('Failed to send input to backend')
        }
      } catch (error) {
        console.error('Error sending input:', error)
      }

      // Clear input and stop waiting
      setCurrentInput('')
      setIsWaitingForInput(false)
      setCurrentPrompt('')
    }
  }

  const formatLine = (line: string, index: number) => {
    // Color different types of output
    if (line.startsWith('>>>') || line.startsWith('...')) {
      return <span key={index} className="text-blue-400">{line}</span>
    }
    if (line.includes('Error') || line.includes('Exception')) {
      return <span key={index} className="text-red-400">{line}</span>
    }
    if (line.includes('Warning')) {
      return <span key={index} className="text-yellow-400">{line}</span>
    }
    if (line.match(/^\[.*\]$/)) {
      return <span key={index} className="text-purple-400">{line}</span>
    }
    return <span key={index} className="text-green-400">{line}</span>
  }

  return (
    <div className="bg-gray-900 text-green-400 rounded-lg border border-gray-700 overflow-hidden font-mono text-sm">
      {/* Terminal Header */}
      <div className="flex items-center gap-2 px-4 py-2 bg-gray-800 border-b border-gray-700">
        <div className="flex gap-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        </div>
        <span className="text-gray-400 text-xs ml-2">Interactive Python Terminal</span>
      </div>

      {/* Terminal Content */}
      <div 
        ref={terminalRef}
        className="p-4 max-h-96 overflow-y-auto space-y-1"
      >
        {/* Display history */}
        {terminalHistory.map((line, index) => (
          <div key={index} className="whitespace-pre-wrap">
            {formatLine(line, index)}
          </div>
        ))}

        {/* Current input line */}
        {isWaitingForInput && (
          <div className="flex items-center">
            <span className="text-blue-400 mr-2">{'>'}</span>
            <input
              ref={inputRef}
              type="text"
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyDown={handleInputSubmit}
              className="bg-transparent border-none outline-none text-green-400 flex-1 font-mono"
              placeholder="Type your input and press Enter..."
              autoComplete="off"
            />
            <span className="text-gray-500 animate-pulse">|</span>
          </div>
        )}

        {/* Show cursor when not waiting for input */}
        {!isWaitingForInput && (
          <div className="flex items-center">
            <span className="text-blue-400 mr-2">{'>>>'}</span>
            <span className="text-gray-500 animate-pulse">|</span>
          </div>
        )}
      </div>

      {/* Help text */}
      {isWaitingForInput && (
        <div className="px-4 py-2 bg-gray-800 border-t border-gray-700 text-xs text-gray-400">
          💡 Type your response and press Enter to continue
        </div>
      )}
    </div>
  )
}
